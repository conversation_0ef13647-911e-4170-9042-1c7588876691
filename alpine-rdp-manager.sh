#!/bin/bash

# 🏔️ Ultra-Lightweight Alpine RDP Manager for DAT Load Board
# This script manages Alpine Docker containers for maximum performance

set -e

CONTAINER_NAME="alpine-rdp-dat"
IMAGE_NAME="danielguerra/alpine-xfce4-xrdp"
RDP_PORT="3390"
MANAGER_PASSWORD="qTi8CBkYgGiCNupJ"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}🏔️ ===============================================${NC}"
    echo -e "${BLUE}   ULTRA-LIGHTWEIGHT ALPINE RDP MANAGER${NC}"
    echo -e "${BLUE}   Lightning-Fast DAT Load Board Access${NC}"
    echo -e "${BLUE}===============================================${NC}"
}

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed!"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running!"
        exit 1
    fi
    
    print_status "Docker is ready"
}

pull_alpine_image() {
    print_status "Pulling ultra-lightweight Alpine XFCE RDP image..."
    docker pull $IMAGE_NAME
    print_status "Alpine image ready (only ~200MB total!)"
}

create_container() {
    print_status "Creating Alpine RDP container..."
    
    # Stop and remove existing container if it exists
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    
    # Create new container with optimized settings
    docker run -d \
        --name $CONTAINER_NAME \
        --restart unless-stopped \
        -p $RDP_PORT:3389 \
        -e USER=manager \
        -e PASSWORD=$MANAGER_PASSWORD \
        --shm-size=1g \
        --memory=2g \
        --cpus=2 \
        $IMAGE_NAME
    
    print_status "Container created and running"
}

setup_chrome_autostart() {
    print_status "Setting up Chrome auto-start for DAT Load Board..."
    
    # Wait for container to be ready
    sleep 5
    
    # Create autostart directory and Chrome launcher
    docker exec $CONTAINER_NAME mkdir -p /home/<USER>/.config/autostart
    
    docker exec $CONTAINER_NAME bash -c 'cat > /home/<USER>/.config/autostart/chrome-dat.desktop << EOF
[Desktop Entry]
Type=Application
Name=Chrome DAT Load Board
Exec=chromium-browser --no-sandbox --start-maximized --disable-dev-shm-usage https://power.dat.com/search/loads
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
EOF'
    
    # Set proper ownership
    docker exec $CONTAINER_NAME chown -R manager:manager /home/<USER>/.config
    
    print_status "Chrome auto-start configured"
}

install_chrome() {
    print_status "Installing Chromium browser..."
    
    docker exec $CONTAINER_NAME apk update
    docker exec $CONTAINER_NAME apk add --no-cache chromium font-noto
    
    print_status "Chromium installed"
}

show_connection_info() {
    echo ""
    echo -e "${GREEN}🎉 ALPINE RDP SETUP COMPLETE! 🎉${NC}"
    echo ""
    echo -e "${BLUE}📋 Connection Details:${NC}"
    echo -e "   Server: ${YELLOW}$(curl -s ifconfig.me):$RDP_PORT${NC}"
    echo -e "   Username: ${YELLOW}manager${NC}"
    echo -e "   Password: ${YELLOW}$MANAGER_PASSWORD${NC}"
    echo ""
    echo -e "${BLUE}🚀 Performance Benefits:${NC}"
    echo -e "   ✅ Ultra-lightweight Alpine Linux (~200MB total)"
    echo -e "   ✅ Lightning-fast XFCE desktop"
    echo -e "   ✅ Minimal resource usage"
    echo -e "   ✅ Auto-launching Chrome with DAT Load Board"
    echo -e "   ✅ Optimized for maximum speed"
    echo ""
    echo -e "${GREEN}Connect now and experience the speed difference! 🚛⚡${NC}"
}

show_status() {
    echo ""
    echo -e "${BLUE}📊 Container Status:${NC}"
    docker ps --filter name=$CONTAINER_NAME --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo ""
    echo -e "${BLUE}💾 Resource Usage:${NC}"
    docker stats $CONTAINER_NAME --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
}

stop_container() {
    print_status "Stopping Alpine RDP container..."
    docker stop $CONTAINER_NAME
    print_status "Container stopped"
}

start_container() {
    print_status "Starting Alpine RDP container..."
    docker start $CONTAINER_NAME
    print_status "Container started"
}

restart_container() {
    print_status "Restarting Alpine RDP container..."
    docker restart $CONTAINER_NAME
    print_status "Container restarted"
}

remove_container() {
    print_warning "Removing Alpine RDP container..."
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    print_status "Container removed"
}

case "${1:-setup}" in
    setup)
        print_header
        check_docker
        pull_alpine_image
        create_container
        install_chrome
        setup_chrome_autostart
        show_connection_info
        ;;
    status)
        show_status
        ;;
    start)
        start_container
        ;;
    stop)
        stop_container
        ;;
    restart)
        restart_container
        ;;
    remove)
        remove_container
        ;;
    info)
        show_connection_info
        ;;
    *)
        echo "Usage: $0 {setup|status|start|stop|restart|remove|info}"
        echo ""
        echo "Commands:"
        echo "  setup   - Create and configure Alpine RDP container"
        echo "  status  - Show container status and resource usage"
        echo "  start   - Start the container"
        echo "  stop    - Stop the container"
        echo "  restart - Restart the container"
        echo "  remove  - Remove the container"
        echo "  info    - Show connection information"
        exit 1
        ;;
esac
