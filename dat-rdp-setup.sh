#!/bin/bash

# 🚛 DAT Load Board RDP Setup - Ultra High Performance
# Optimized for zero lag, maximum responsiveness

set -e

echo "🚛 Setting up ultra-high performance DAT Load Board RDP access..."

# Configure XRDP for maximum performance
echo "⚡ Configuring XRDP for maximum performance..."

# Backup original config
cp /etc/xrdp/xrdp.ini /etc/xrdp/xrdp.ini.backup 2>/dev/null || true

# Create optimized XRDP configuration
cat > /etc/xrdp/xrdp.ini << 'EOF'
[Globals]
; xrdp.ini file version number
ini_version=1

; fork a new process for each incoming connection
fork=true

; ports to listen on, number alone means listen on all interfaces
; 0.0.0.0 or :: if ipv6 is configured
; space between multiple occurrences
; ALL specified interfaces must be 127.0.0.1 or ::1 when security layer is set to 'tls'
port=3390

; 'yes' or 'no' or 'ask'
; security layer can be 'tls', 'rdp' or 'negotiate'
; for client compatible layer
security_layer=rdp
crypt_level=low
certificate=
key_file=
ssl_protocols=TLSv1.2, TLSv1.3

; set SSL cipher suites
#ssl_ciphers=HIGH

; Section name to use for automatic login if the client sends username
; and password. If empty, the domain name sent by the client is used.
; If empty and no domain name is given, the first suitable section in
; this file will be used.
autorun=

allow_channels=true
allow_multimon=true
bitmap_cache=true
bitmap_compression=true
bulk_compression=true

; fastpath - can be 'input', 'output', 'both', 'none'
use_fastpath=both

; when true, userid/password *must* be passed on cmd line
#require_credentials=true

; You can set the PAM error text in a gateway setup (MAX 256 chars)
#pamerrortxt=change your password according to policy at http://url

; colors used by windows in RGB format
blue=009cb5
grey=dedede
#black=000000
#dark_grey=808080
#blue=08246b
#dark_blue=08246b
#white=ffffff
#red=ff0000
#green=00ff00
#background=626c72

; configure login screen
; Login Screen Window Title
#ls_title=My Login Title

; top level window background color in RGB format
ls_top_window_bg_color=009cb5

; width and height of the login screen
; The default height allows for about 5 fields to be comfortably displayed
; above the buttons at the bottom. To display more fields, make <ls_height>
; larger, and also set <ls_btn_ok_y_pos> and <ls_btn_cancel_y_pos> to be
; further down.
; The login screen will be centered on the client window. If the client
; window is smaller than the login screen, the login screen will be
; clipped to fit.
#ls_width=350
#ls_height=430

; login screen background color in RGB format
ls_bg_color=dedede

; optional background image filename (bmp format).
#ls_background_image=

; logo
; full path to bmp-file or file in shared folder
ls_logo_filename=
ls_logo_x_pos=55
ls_logo_y_pos=50

; for positioning labels such as username, password etc
ls_label_x_pos=30
ls_label_width=65

; for positioning text and combo boxes next to above labels
ls_input_x_pos=110
ls_input_width=210

; y pos for first label and combo box
ls_input_y_pos=220

; OK button
ls_btn_ok_x_pos=142
ls_btn_ok_y_pos=370
ls_btn_ok_width=85
ls_btn_ok_height=30

; Cancel button
ls_btn_cancel_x_pos=237
ls_btn_cancel_y_pos=370
ls_btn_cancel_width=85
ls_btn_cancel_height=30

[Xorg]
name=Xorg
lib=libxup.so
username=ask
password=ask
ip=127.0.0.1
port=-1
code=20

[Xvnc]
name=Xvnc
lib=libvnc.so
username=ask
password=ask
ip=127.0.0.1
port=-1
#xserverbpp=24
#delay_ms=2000

[vnc-any]
name=vnc-any
lib=libvnc.so
ip=ask
port=ask5900
username=na
password=ask

[sesman-any]
name=sesman-any
lib=libvnc.so
ip=ask
port=ask5900
username=na
password=ask

[neutrinordp-any]
name=neutrinordp-any
lib=libneutrinordp.so
ip=ask
port=ask3389
username=ask
password=ask

[Xorg-session]
name=Xorg-session
lib=libxup.so
username=ask
password=ask
ip=127.0.0.1
port=-1
code=10
EOF

# Configure SESMAN for performance
cat > /etc/xrdp/sesman.ini << 'EOF'
[Globals]
ListenAddress=127.0.0.1
ListenPort=3350
EnableUserWindowManager=true
; Give in relative path to user's home directory
UserWindowManager=startwm.sh
; Give in full path or relative path to /etc/xrdp
DefaultWindowManager=startwm.sh
; Give in full path or relative path to /etc/xrdp
ReconnectScript=reconnectwm.sh

[Security]
AllowRootLogin=true
MaxLoginRetry=4
TerminalServerUsers=tsusers
TerminalServerAdmins=tsadmins
; When AlwaysGroupCheck=false access will be permitted
; if the group TerminalServerUsers is not defined.
AlwaysGroupCheck=false
; When RestrictOutboundClipboard=all clipboard from the
; server is not pushed to the client.
; In addition, you can control text/file/image transfer restrictions
; respectively. It also accepts comma separated list such as text,file.
RestrictOutboundClipboard=none
; When RestrictInboundClipboard=all clipboard from the
; client is not pushed to the server.
; In addition, you can control text/file/image transfer restrictions
; respectively. It also accepts comma separated list such as text,file.
RestrictInboundClipboard=none

[Sessions]
;; X11DisplayOffset - x11 display number offset
; Type: integer
; Default: 10
X11DisplayOffset=10

;; MaxSessions - maximum number of connections to an xrdp server
; Type: integer
; Default: 0
MaxSessions=50

;; KillDisconnected - kill disconnected sessions
; Type: boolean
; Default: false
; if 1, true, or yes, every session will be killed within 60 seconds
; after the user disconnects
KillDisconnected=false

;; DisconnectedTimeLimit (seconds) - wait before kill disconnected sessions
; Type: integer
; Default: 0
; if KillDisconnected is set to false, this value is ignored
DisconnectedTimeLimit=0

;; IdleTimeLimit (seconds) - wait before disconnect idle sessions
; Type: integer
; Default: 0
; Set to 0 to disable idle disconnection.
IdleTimeLimit=0

;; Policy - session allocation policy
; Type: enum [ "Default" | "UBD" | "UBI" | "UBC" | "UBDI" | "UBDC" ]
; "Default" session per <User,BitPerPixel>
; "UBD" session per <User,BitPerPixel,DisplaySize>
; "UBI" session per <User,BitPerPixel,IPAddr>
; "UBC" session per <User,BitPerPixel,Connection>
; "UBDI" session per <User,BitPerPixel,DisplaySize,IPAddr>
; "UBDC" session per <User,BitPerPixel,DisplaySize,Connection>
Policy=Default

[Logging]
LogFile=xrdp-sesman.log
LogLevel=INFO
EnableSyslog=true
SyslogLevel=INFO

[LoggingPerLogger]
; Note: per logger configuration is only used if xrdp is built with
; --enable-devel-logging
;sesman.c=INFO
;main()=INFO

[Chansrv]
; drive redirection
; See sesman.ini(5) for the format of this parameter
FuseMountName=thinclient_drives

[SessionVariables]
PULSE_SERVER=unix:/tmp/.pulse-native
EOF

# Create optimized startup script
cat > /etc/xrdp/startwm.sh << 'EOF'
#!/bin/sh
# xrdp X session start script (c) 2015, 2017, 2021 mirabilos
# published under The MirOS Licence

# Rely on /etc/pam.d/xrdp-sesman using pam_env to load both
# /etc/environment and /etc/security/pam_env.conf

if test -r /etc/profile; then
	. /etc/profile
fi

if test -r /etc/default/locale; then
	. /etc/default/locale
	test -z "${LANG+x}" || export LANG
	test -z "${LANGUAGE+x}" || export LANGUAGE
	test -z "${LC_ADDRESS+x}" || export LC_ADDRESS
	test -z "${LC_ALL+x}" || export LC_ALL
	test -z "${LC_COLLATE+x}" || export LC_COLLATE
	test -z "${LC_CTYPE+x}" || export LC_CTYPE
	test -z "${LC_IDENTIFICATION+x}" || export LC_IDENTIFICATION
	test -z "${LC_MEASUREMENT+x}" || export LC_MEASUREMENT
	test -z "${LC_MESSAGES+x}" || export LC_MESSAGES
	test -z "${LC_MONETARY+x}" || export LC_MONETARY
	test -z "${LC_NAME+x}" || export LC_NAME
	test -z "${LC_NUMERIC+x}" || export LC_NUMERIC
	test -z "${LC_PAPER+x}" || export LC_PAPER
	test -z "${LC_TELEPHONE+x}" || export LC_TELEPHONE
	test -z "${LC_TIME+x}" || export LC_TIME
fi

if test -r /etc/xrdp/xrdp_keyboard.ini; then
	XRDP_KEYBOARD=/etc/xrdp/xrdp_keyboard.ini
fi
if test -r ~/.xrdp_keyboard.ini; then
	XRDP_KEYBOARD=~/.xrdp_keyboard.ini
fi
if test -n "$XRDP_KEYBOARD"; then
	setxkbmap -rules evdev -model pc104 -layout us
fi

# Performance optimizations
export XFCE4_SESSION_DISABLE_AUTOSTART=1
export XFCE4_DISABLE_COMPOSITING=1

# Start XFCE4 session with optimizations
exec xfce4-session
EOF

chmod +x /etc/xrdp/startwm.sh

# Enable and start XRDP
systemctl enable xrdp
systemctl restart xrdp

# Configure firewall
ufw allow 3390/tcp 2>/dev/null || true

echo "✅ XRDP configured for maximum performance on port 3390"

# Create user management script
cat > /usr/local/bin/manage-dat-users << 'EOF'
#!/bin/bash

# DAT User Management Script

case "$1" in
    "add")
        if [ -z "$2" ]; then
            echo "Usage: manage-dat-users add <username>"
            exit 1
        fi
        
        USERNAME="$2"
        PASSWORD=$(openssl rand -base64 12)
        
        # Create user
        useradd -m -s /bin/bash "$USERNAME" 2>/dev/null || true
        echo "$USERNAME:$PASSWORD" | chpasswd
        
        # Add to RDP group
        usermod -aG ssl-cert "$USERNAME" 2>/dev/null || true
        
        # Create XFCE config directory
        mkdir -p "/home/<USER>/.config/xfce4/xfconf/xfce-perchannel-xml"
        
        # Configure XFCE for performance
        cat > "/home/<USER>/.config/xfce4/xfconf/xfce-perchannel-xml/xfwm4.xml" << 'XFCE_EOF'
<?xml version="1.0" encoding="UTF-8"?>
<channel name="xfwm4" version="1.0">
  <property name="general" type="empty">
    <property name="activate_action" type="string" value="bring"/>
    <property name="borderless_maximize" type="bool" value="true"/>
    <property name="box_move" type="bool" value="false"/>
    <property name="box_resize" type="bool" value="false"/>
    <property name="button_layout" type="string" value="O|SHMC"/>
    <property name="button_offset" type="int" value="0"/>
    <property name="button_spacing" type="int" value="0"/>
    <property name="click_to_focus" type="bool" value="true"/>
    <property name="cycle_apps_only" type="bool" value="false"/>
    <property name="cycle_draw_frame" type="bool" value="true"/>
    <property name="cycle_raise" type="bool" value="false"/>
    <property name="cycle_hidden" type="bool" value="true"/>
    <property name="cycle_minimum" type="bool" value="true"/>
    <property name="cycle_preview" type="bool" value="true"/>
    <property name="cycle_tabwin_mode" type="int" value="0"/>
    <property name="cycle_workspaces" type="bool" value="false"/>
    <property name="double_click_action" type="string" value="maximize"/>
    <property name="double_click_distance" type="int" value="5"/>
    <property name="double_click_time" type="int" value="250"/>
    <property name="easy_click" type="string" value="Alt"/>
    <property name="focus_delay" type="int" value="10"/>
    <property name="focus_hint" type="bool" value="true"/>
    <property name="focus_new" type="bool" value="true"/>
    <property name="frame_opacity" type="int" value="100"/>
    <property name="frame_border_top" type="int" value="0"/>
    <property name="full_width_title" type="bool" value="true"/>
    <property name="horiz_scroll_opacity" type="bool" value="false"/>
    <property name="inactive_opacity" type="int" value="100"/>
    <property name="maximized_offset" type="int" value="0"/>
    <property name="mousewheel_rollup" type="bool" value="true"/>
    <property name="move_opacity" type="int" value="100"/>
    <property name="placement_mode" type="string" value="center"/>
    <property name="placement_ratio" type="int" value="20"/>
    <property name="popup_opacity" type="int" value="100"/>
    <property name="prevent_focus_stealing" type="bool" value="false"/>
    <property name="raise_delay" type="int" value="250"/>
    <property name="raise_on_click" type="bool" value="true"/>
    <property name="raise_on_focus" type="bool" value="false"/>
    <property name="raise_with_any_button" type="bool" value="true"/>
    <property name="repeat_urgent_blink" type="bool" value="false"/>
    <property name="resize_opacity" type="int" value="100"/>
    <property name="restore_on_move" type="bool" value="true"/>
    <property name="scroll_workspaces" type="bool" value="true"/>
    <property name="shadow_delta_height" type="int" value="0"/>
    <property name="shadow_delta_width" type="int" value="0"/>
    <property name="shadow_delta_x" type="int" value="0"/>
    <property name="shadow_delta_y" type="int" value="-3"/>
    <property name="shadow_opacity" type="int" value="50"/>
    <property name="show_app_icon" type="bool" value="false"/>
    <property name="show_dock_shadow" type="bool" value="true"/>
    <property name="show_frame_shadow" type="bool" value="true"/>
    <property name="show_popup_shadow" type="bool" value="false"/>
    <property name="snap_resist" type="bool" value="false"/>
    <property name="snap_to_border" type="bool" value="true"/>
    <property name="snap_to_windows" type="bool" value="false"/>
    <property name="snap_width" type="int" value="10"/>
    <property name="vblank_mode" type="string" value="auto"/>
    <property name="theme" type="string" value="Default"/>
    <property name="tile_on_move" type="bool" value="true"/>
    <property name="title_alignment" type="string" value="center"/>
    <property name="title_font" type="string" value="Sans Bold 9"/>
    <property name="title_horizontal_offset" type="int" value="0"/>
    <property name="title_shadow_active" type="string" value="false"/>
    <property name="title_shadow_inactive" type="string" value="false"/>
    <property name="title_vertical_offset_active" type="int" value="0"/>
    <property name="title_vertical_offset_inactive" type="int" value="0"/>
    <property name="toggle_workspaces" type="bool" value="false"/>
    <property name="unredirect_overlays" type="bool" value="true"/>
    <property name="urgent_blink" type="bool" value="false"/>
    <property name="use_compositing" type="bool" value="false"/>
    <property name="workspace_count" type="int" value="4"/>
    <property name="workspace_names" type="array">
      <value type="string" value="Workspace 1"/>
      <value type="string" value="Workspace 2"/>
      <value type="string" value="Workspace 3"/>
      <value type="string" value="Workspace 4"/>
    </property>
    <property name="wrap_cycle" type="bool" value="true"/>
    <property name="wrap_layout" type="bool" value="true"/>
    <property name="wrap_resistance" type="int" value="10"/>
    <property name="wrap_windows" type="bool" value="true"/>
    <property name="wrap_workspaces" type="bool" value="false"/>
    <property name="zoom_desktop" type="bool" value="true"/>
    <property name="zoom_pointer" type="bool" value="true"/>
  </property>
</channel>
XFCE_EOF

        # Create startup script for Chrome
        cat > "/home/<USER>/.xsessionrc" << 'SESSION_EOF'
#!/bin/bash
# Auto-start Chrome with DAT Load Board
export DISPLAY=:10.0
sleep 2
google-chrome --no-sandbox --disable-dev-shm-usage --disable-gpu --disable-software-rasterizer --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-features=TranslateUI --disable-ipc-flooding-protection --user-data-dir="/home/<USER>/.config/google-chrome" "https://power.dat.com/search/loads" &
SESSION_EOF
        
        chmod +x "/home/<USER>/.xsessionrc"
        chown -R "$USERNAME:$USERNAME" "/home/<USER>"
        
        echo "✅ User '$USERNAME' created successfully!"
        echo "🔑 Password: $PASSWORD"
        echo "🌐 RDP: Connect to <server-ip>:3390"
        echo "👤 Username: $USERNAME"
        ;;
        
    "delete")
        if [ -z "$2" ]; then
            echo "Usage: manage-dat-users delete <username>"
            exit 1
        fi
        
        USERNAME="$2"
        userdel -r "$USERNAME" 2>/dev/null || true
        echo "✅ User '$USERNAME' deleted successfully!"
        ;;
        
    "list")
        echo "📋 DAT RDP Users:"
        getent passwd | grep "/home/" | grep -v "testuser" | cut -d: -f1 | while read user; do
            echo "  👤 $user"
        done
        ;;
        
    "sync")
        if [ -z "$2" ] || [ -z "$3" ]; then
            echo "Usage: manage-dat-users sync <from_user> <to_user>"
            exit 1
        fi
        
        FROM_USER="$2"
        TO_USER="$3"
        
        if [ ! -d "/home/<USER>" ] || [ ! -d "/home/<USER>" ]; then
            echo "❌ One or both users don't exist"
            exit 1
        fi
        
        # Copy Chrome profile
        rsync -av "/home/<USER>/.config/google-chrome/" "/home/<USER>/.config/google-chrome/" 2>/dev/null || true
        chown -R "$TO_USER:$TO_USER" "/home/<USER>/.config/google-chrome/" 2>/dev/null || true
        
        echo "✅ DAT session synced from '$FROM_USER' to '$TO_USER'"
        ;;
        
    *)
        echo "DAT User Management"
        echo "Usage:"
        echo "  manage-dat-users add <username>     - Create new user"
        echo "  manage-dat-users delete <username>  - Delete user"
        echo "  manage-dat-users list               - List all users"
        echo "  manage-dat-users sync <from> <to>   - Sync DAT session between users"
        ;;
esac
EOF

chmod +x /usr/local/bin/manage-dat-users

# Create session sync script
cat > /usr/local/bin/sync-all-dat-sessions << 'EOF'
#!/bin/bash

if [ -z "$1" ]; then
    echo "Usage: sync-all-dat-sessions <main_user>"
    echo "This will copy the DAT session from <main_user> to all other users"
    exit 1
fi

MAIN_USER="$1"

if [ ! -d "/home/<USER>" ]; then
    echo "❌ User '$MAIN_USER' doesn't exist"
    exit 1
fi

echo "🔄 Syncing DAT session from '$MAIN_USER' to all other users..."

getent passwd | grep "/home/" | grep -v "testuser" | grep -v "$MAIN_USER" | cut -d: -f1 | while read user; do
    echo "  📋 Syncing to $user..."
    manage-dat-users sync "$MAIN_USER" "$user"
done

echo "✅ All DAT sessions synced from '$MAIN_USER'!"
EOF

chmod +x /usr/local/bin/sync-all-dat-sessions

echo ""
echo "🎉 DAT Load Board RDP setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Create your first user: sudo manage-dat-users add manager"
echo "2. Connect via RDP to <server-ip>:3390"
echo "3. Login to DAT Load Board in Chrome"
echo "4. Create more users: sudo manage-dat-users add employee1"
echo "5. Sync sessions: sudo sync-all-dat-sessions manager"
echo ""
echo "🚀 Your employees will have lightning-fast DAT access!"
