#!/bin/bash

echo "🔧 Fixing RDP Black Screen Issue..."

# Stop services
systemctl stop xrdp xrdp-sesman

# Install required packages
apt update
apt install -y ubuntu-desktop-minimal tigervnc-standalone-server

# Create simple working startup script
cat > /etc/xrdp/startwm.sh << 'EOF'
#!/bin/bash
unset SESSION_MANAGER
unset DBUS_SESSION_BUS_ADDRESS
export PATH=/usr/local/bin:/usr/bin:/bin
export GNOME_SHELL_SESSION_MODE=ubuntu
export XDG_CURRENT_DESKTOP=ubuntu:GNOME
export XDG_SESSION_DESKTOP=ubuntu
export XDG_SESSION_TYPE=x11
gnome-session --session=ubuntu
EOF

chmod +x /etc/xrdp/startwm.sh

# Configure XRDP for VNC
cat > /etc/xrdp/xrdp.ini << 'EOF'
[Globals]
ini_version=1
fork=true
port=3390
security_layer=negotiate
crypt_level=high
certificate=
key_file=
ssl_protocols=TLSv1.2, TLSv1.3
allow_channels=true
allow_multimon=true
bitmap_cache=true
bitmap_compression=true
bulk_compression=true
use_fastpath=both
autorun=
blue=009cb5
grey=dedede

[Xvnc]
name=Xvnc
lib=libvnc.so
username=ask
password=ask
ip=127.0.0.1
port=-1

[Xorg]
name=Xorg
lib=libxup.so
username=ask
password=ask
ip=127.0.0.1
port=-1
code=20
EOF

# Fix user permissions
chown -R manager:manager /home/<USER>/dev/null || true
chown -R employee1:employee1 /home/<USER>/dev/null || true

# Create VNC startup for users
cat > /home/<USER>/.xsessionrc << 'EOF'
#!/bin/bash
export GNOME_SHELL_SESSION_MODE=ubuntu
export XDG_CURRENT_DESKTOP=ubuntu:GNOME
export XDG_SESSION_DESKTOP=ubuntu
sleep 5
google-chrome --no-sandbox --start-maximized "https://power.dat.com/search/loads" &
EOF

chmod +x /home/<USER>/.xsessionrc
chown manager:manager /home/<USER>/.xsessionrc

# Start services
systemctl enable xrdp xrdp-sesman
systemctl start xrdp xrdp-sesman

echo "✅ RDP Black Screen Fix Complete!"
echo "🌐 Connect to: <server-ip>:3390"
echo "👤 Username: manager"
echo "🔑 Password: qTi8CBkYgGiCNupJ"
echo "📋 Select 'Xvnc' session type when connecting"
