# 🔧 RDP CONNECTION TROUBLESHOOTING GUIDE

## 📋 **CONNECTION DETAILS**
- **Server:** `*************:3390`
- **Username:** `alpine`
- **Password:** `qTi8CBkYgGiCNupJ`

## 🚨 **COMMON ISSUES & SOLUTIONS**

### **1. "Connection Failed" or "Cannot Connect"**
**Possible Causes:**
- Firewall blocking port 3390
- Wrong server address
- Container not running

**Solutions:**
```bash
# Check if container is running
docker ps | grep alpine-rdp

# Restart container
/opt/alpine-rdp-manager.sh restart

# Check port is open
netstat -tlnp | grep 3390
```

### **2. "Authentication Failed" or "Login Failed"**
**Possible Causes:**
- Wrong username/password
- User account issues

**Solutions:**
```bash
# Reset password
docker exec alpine-rdp-dat sh -c 'echo "alpine:qTi8CBkYgGiCNupJ" | chpasswd'

# Verify user exists
docker exec alpine-rdp-dat id alpine
```

### **3. "Black Screen" or "Session Starts but No Desktop"**
**Possible Causes:**
- Desktop environment not starting
- Display server issues

**Solutions:**
```bash
# Check XFCE is installed
docker exec alpine-rdp-dat which startxfce4

# Restart container
/opt/alpine-rdp-manager.sh restart
```

### **4. "Connection Drops Immediately"**
**Possible Causes:**
- Session configuration issues
- Resource limits

**Solutions:**
```bash
# Check container logs
docker logs alpine-rdp-dat --tail 20

# Check resource usage
docker stats alpine-rdp-dat --no-stream
```

## 🔍 **STEP-BY-STEP DEBUGGING**

### **Step 1: Verify Container Status**
```bash
/opt/alpine-rdp-manager.sh status
```
Should show: "Up X seconds" and low resource usage

### **Step 2: Test Local Connection**
```bash
telnet localhost 3390
```
Should connect successfully (Ctrl+C to exit)

### **Step 3: Check RDP Services**
```bash
docker exec alpine-rdp-dat ps aux | grep xrdp
```
Should show xrdp and xrdp-sesman running

### **Step 4: Verify User Account**
```bash
docker exec alpine-rdp-dat id alpine
docker exec alpine-rdp-dat sh -c 'echo "alpine:qTi8CBkYgGiCNupJ" | chpasswd'
```

### **Step 5: Check Logs for Errors**
```bash
docker logs alpine-rdp-dat --tail 50
```

## 🖥️ **CLIENT-SPECIFIC ISSUES**

### **Windows Remote Desktop**
- Make sure to include port: `*************:3390`
- Try "Connect anyway" if certificate warning appears
- Check "Allow me to save credentials"

### **Mac Microsoft Remote Desktop**
- Add PC with full address including port
- Set username/password in connection settings
- Try different display settings if issues persist

### **Linux RDP Clients**
```bash
# Using rdesktop
rdesktop -u alpine -p qTi8CBkYgGiCNupJ *************:3390

# Using xfreerdp
xfreerdp /u:alpine /p:qTi8CBkYgGiCNupJ /v:*************:3390
```

## 🆘 **EMERGENCY RESET**

If nothing works, completely reset the container:
```bash
# Remove and recreate
/opt/alpine-rdp-manager.sh remove
/opt/alpine-rdp-manager.sh setup
```

## 📞 **WHAT ERROR ARE YOU SEEING?**

Please share the specific error message you're getting:
- "Connection failed"
- "Authentication failed" 
- "Black screen"
- "Connection drops"
- Other: _______________

This will help pinpoint the exact issue!
