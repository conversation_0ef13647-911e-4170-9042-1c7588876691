FROM alpine:latest

# Install base packages
RUN apk update && apk add --no-cache \
    xfce4 \
    xfce4-terminal \
    xfce4-screensaver \
    xrdp \
    supervisor \
    dbus \
    firefox \
    thunar \
    mousepad \
    sudo \
    bash \
    curl \
    wget \
    git \
    nano \
    htop \
    openssh-client \
    ca-certificates \
    tzdata

# Create user
RUN adduser -D -s /bin/bash datuser && \
    echo "datuser:datuser123" | chpasswd && \
    addgroup datuser wheel && \
    echo '%wheel ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers

# Configure XRDP
RUN echo "exec startxfce4" > /home/<USER>/.xsession && \
    chown datuser:datuser /home/<USER>/.xsession && \
    chmod +x /home/<USER>/.xsession

# Create XRDP config
RUN mkdir -p /etc/xrdp && \
    echo "[Globals]" > /etc/xrdp/xrdp.ini && \
    echo "bitmap_cache=true" >> /etc/xrdp/xrdp.ini && \
    echo "bitmap_compression=true" >> /etc/xrdp/xrdp.ini && \
    echo "port=3389" >> /etc/xrdp/xrdp.ini && \
    echo "crypt_level=low" >> /etc/xrdp/xrdp.ini && \
    echo "channel_code=1" >> /etc/xrdp/xrdp.ini && \
    echo "" >> /etc/xrdp/xrdp.ini && \
    echo "[xrdp1]" >> /etc/xrdp/xrdp.ini && \
    echo "name=sesman-Xvnc" >> /etc/xrdp/xrdp.ini && \
    echo "lib=libvnc.so" >> /etc/xrdp/xrdp.ini && \
    echo "username=ask" >> /etc/xrdp/xrdp.ini && \
    echo "password=ask" >> /etc/xrdp/xrdp.ini && \
    echo "ip=127.0.0.1" >> /etc/xrdp/xrdp.ini && \
    echo "port=-1" >> /etc/xrdp/xrdp.ini

# Configure sesman
RUN echo "[Globals]" > /etc/xrdp/sesman.ini && \
    echo "ListenAddress=127.0.0.1" >> /etc/xrdp/sesman.ini && \
    echo "ListenPort=3350" >> /etc/xrdp/sesman.ini && \
    echo "EnableUserWindowManager=true" >> /etc/xrdp/sesman.ini && \
    echo "UserWindowManager=startxfce4" >> /etc/xrdp/sesman.ini && \
    echo "DefaultWindowManager=startxfce4" >> /etc/xrdp/sesman.ini && \
    echo "" >> /etc/xrdp/sesman.ini && \
    echo "[Security]" >> /etc/xrdp/sesman.ini && \
    echo "AllowRootLogin=false" >> /etc/xrdp/sesman.ini && \
    echo "MaxLoginRetry=4" >> /etc/xrdp/sesman.ini && \
    echo "TerminalServerUsers=datuser" >> /etc/xrdp/sesman.ini && \
    echo "TerminalServerAdmins=datuser" >> /etc/xrdp/sesman.ini && \
    echo "" >> /etc/xrdp/sesman.ini && \
    echo "[Sessions]" >> /etc/xrdp/sesman.ini && \
    echo "X11DisplayOffset=10" >> /etc/xrdp/sesman.ini && \
    echo "MaxSessions=10" >> /etc/xrdp/sesman.ini && \
    echo "KillDisconnected=false" >> /etc/xrdp/sesman.ini && \
    echo "IdleTimeLimit=0" >> /etc/xrdp/sesman.ini && \
    echo "DisconnectedTimeLimit=0" >> /etc/xrdp/sesman.ini

# Create supervisor config
RUN mkdir -p /etc/supervisor/conf.d
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Create startup script
COPY startup.sh /startup.sh
RUN chmod +x /startup.sh

# Create directories
RUN mkdir -p /var/run/xrdp && \
    mkdir -p /var/log/xrdp && \
    mkdir -p /tmp/.X11-unix && \
    chmod 1777 /tmp/.X11-unix

# Set permissions
RUN chown -R datuser:datuser /home/<USER>

EXPOSE 3389

CMD ["/startup.sh"]
