# DAT - Desktop as a Service (Alpine Edition)

A modern, lightweight Desktop as a Service solution built on Alpine Linux with XFCE desktop environment and XRDP for remote access.

## Features

- 🏔️ **Alpine Linux Base** - Minimal, secure, and fast
- 🖥️ **XFCE Desktop** - Lightweight and user-friendly desktop environment
- 🔗 **XRDP Access** - Standard RDP protocol for universal client compatibility
- 📊 **Web Management** - Real-time monitoring and control dashboard
- 🐳 **Docker Containerized** - Easy deployment and scaling
- 🔧 **Supervisor Process Management** - Reliable service orchestration

## Quick Start

1. **Build and Start Services**
   ```bash
   cd /opt/dat-alpine
   docker-compose up -d --build
   ```

2. **Access the Desktop**
   - **RDP Client**: Connect to `your-server-ip:3389`
   - **Username**: `datuser`
   - **Password**: `datuser123`

3. **Management Dashboard**
   - Open browser to `http://your-server-ip:8080`
   - Monitor system resources and container health
   - Restart services as needed

## Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   RDP Client    │────│  dat-desktop    │
│                 │    │  (Alpine+XFCE)  │
└─────────────────┘    └─────────────────┘
                                │
┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │────│   dat-manager   │
│                 │    │   (Dashboard)   │
└─────────────────┘    └─────────────────┘
```

## Services

### dat-desktop
- **Base**: Alpine Linux with XFCE4
- **Port**: 3389 (RDP)
- **User**: datuser / datuser123
- **Features**: Firefox, file manager, terminal, text editor

### dat-manager
- **Base**: Python Flask application
- **Port**: 8080 (HTTP)
- **Features**: System monitoring, container management, health checks

## Configuration

### Environment Variables
- `DAT_DESKTOP_HOST`: Desktop container hostname (default: dat-desktop)
- `DAT_DESKTOP_PORT`: Desktop RDP port (default: 3389)

### Volumes
- `dat_home`: Persistent user home directory
- `/tmp/.X11-unix`: X11 socket sharing

## Management Commands

```bash
# Start services
docker-compose up -d

# View logs
docker-compose logs -f

# Restart desktop only
docker-compose restart dat-desktop

# Stop all services
docker-compose down

# Rebuild and restart
docker-compose up -d --build --force-recreate
```

## Troubleshooting

### Desktop Connection Issues
1. Check container status: `docker-compose ps`
2. View desktop logs: `docker-compose logs dat-desktop`
3. Verify port accessibility: `netstat -tlnp | grep 3389`
4. Use management dashboard to restart services

### Performance Optimization
- Adjust container resource limits in docker-compose.yml
- Monitor system resources via dashboard
- Scale horizontally by running multiple desktop instances

## Security Considerations

- Change default password in production
- Use VPN or secure network for RDP access
- Implement proper firewall rules
- Regular security updates for base images

## Customization

### Adding Software
Edit `Dockerfile` and add packages to the `apk add` command:
```dockerfile
RUN apk add --no-cache \
    your-package-here
```

### Desktop Environment
Modify XFCE configuration in the container or mount custom configs as volumes.

### User Management
Extend the management dashboard to support multiple users and dynamic provisioning.

## Support

For issues and feature requests, check the logs and management dashboard first. The system is designed to be self-healing with automatic restarts and health monitoring.
