[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:dbus]
command=/usr/bin/dbus-daemon --system --nofork --nopidfile
user=root
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/dbus.log
stderr_logfile=/var/log/supervisor/dbus.log
priority=100

[program:xrdp-sesman]
command=/usr/sbin/xrdp-sesman --nodaemon
user=root
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/xrdp-sesman.log
stderr_logfile=/var/log/supervisor/xrdp-sesman.log
priority=200
depends_on=dbus

[program:xrdp]
command=/usr/sbin/xrdp --nodaemon
user=root
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/xrdp.log
stderr_logfile=/var/log/supervisor/xrdp.log
priority=300
depends_on=xrdp-sesman
