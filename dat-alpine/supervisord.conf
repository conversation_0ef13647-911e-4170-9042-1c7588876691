[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:dbus]
command=/usr/bin/dbus-daemon --system --nofork
user=root
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/dbus.log
stderr_logfile=/var/log/supervisor/dbus.log

[program:xrdp-sesman]
command=/usr/sbin/xrdp-sesman --nodaemon
user=root
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/xrdp-sesman.log
stderr_logfile=/var/log/supervisor/xrdp-sesman.log

[program:xrdp]
command=/usr/sbin/xrdp --nodaemon
user=root
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/xrdp.log
stderr_logfile=/var/log/supervisor/xrdp.log
depends_on=xrdp-sesman
