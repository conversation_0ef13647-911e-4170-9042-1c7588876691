Section "ServerLayout"
    Identifier     "Layout0"
    Screen      0  "Screen0"
    InputDevice    "Keyboard0" "CoreKeyboard"
    InputDevice    "Mouse0" "CorePointer"
EndSection

Section "Files"
    ModulePath   "/usr/lib/xorg/modules"
    FontPath     "/usr/share/fonts/misc/"
    FontPath     "/usr/share/fonts/TTF/"
    FontPath     "/usr/share/fonts/OTF/"
    FontPath     "/usr/share/fonts/Type1/"
    FontPath     "/usr/share/fonts/100dpi/"
    FontPath     "/usr/share/fonts/75dpi/"
EndSection

Section "Module"
    Load  "dbe"
    Load  "extmod"
    Load  "type1"
    Load  "freetype"
    Load  "glx"
EndSection

Section "InputDevice"
    Identifier  "Mouse0"
    Driver      "libinput"
    Option      "Protocol" "auto"
    Option      "Device" "/dev/input/mice"
    Option      "Emulate3Buttons" "no"
    Option      "ZAxisMapping" "4 5"
EndSection

Section "InputDevice"
    Identifier  "Keyboard0"
    Driver      "libinput"
    Option      "XkbModel" "pc105"
    Option      "XkbLayout" "us"
EndSection

Section "Monitor"
    Identifier   "Monitor0"
    VendorName   "Monitor Vendor"
    ModelName    "Monitor Model"
EndSection

Section "Device"
    Identifier  "Card0"
    Driver      "dummy"
    VideoRam    256000
EndSection

Section "Screen"
    Identifier "Screen0"
    Device     "Card0"
    Monitor    "Monitor0"
    DefaultDepth     24
    SubSection "Display"
        Viewport   0 0
        Depth     24
        Modes    "1024x768" "800x600" "640x480"
    EndSubSection
EndSection
