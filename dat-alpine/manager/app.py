#!/usr/bin/env python3

import os
import json
import docker
import psutil
from flask import Flask, render_template, jsonify, request
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
docker_client = docker.from_env()

class DATManager:
    def __init__(self):
        self.desktop_host = os.getenv('DAT_DESKTOP_HOST', 'dat-desktop')
        self.desktop_port = os.getenv('DAT_DESKTOP_PORT', '3389')
        
    def get_system_stats(self):
        """Get system resource statistics"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_gb': round(memory.used / (1024**3), 2),
                'memory_total_gb': round(memory.total / (1024**3), 2),
                'disk_percent': disk.percent,
                'disk_used_gb': round(disk.used / (1024**3), 2),
                'disk_total_gb': round(disk.total / (1024**3), 2)
            }
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            return {}
    
    def get_container_status(self):
        """Get status of DAT containers"""
        try:
            containers = []
            for container in docker_client.containers.list(all=True):
                if 'dat-' in container.name:
                    containers.append({
                        'name': container.name,
                        'status': container.status,
                        'image': container.image.tags[0] if container.image.tags else 'unknown',
                        'created': container.attrs['Created'],
                        'ports': container.ports
                    })
            return containers
        except Exception as e:
            logger.error(f"Error getting container status: {e}")
            return []
    
    def get_desktop_health(self):
        """Check if desktop service is healthy"""
        try:
            container = docker_client.containers.get('dat-alpine-desktop')
            if container.status == 'running':
                # Check if XRDP port is listening
                exec_result = container.exec_run('netstat -tlnp | grep :3389')
                if exec_result.exit_code == 0:
                    return {'status': 'healthy', 'message': 'Desktop service is running and accessible'}
                else:
                    return {'status': 'unhealthy', 'message': 'Desktop service is running but XRDP port not accessible'}
            else:
                return {'status': 'down', 'message': f'Desktop container is {container.status}'}
        except docker.errors.NotFound:
            return {'status': 'not_found', 'message': 'Desktop container not found'}
        except Exception as e:
            logger.error(f"Error checking desktop health: {e}")
            return {'status': 'error', 'message': str(e)}

dat_manager = DATManager()

@app.route('/')
def dashboard():
    """Main dashboard"""
    return render_template('dashboard.html')

@app.route('/api/status')
def api_status():
    """API endpoint for system status"""
    return jsonify({
        'timestamp': datetime.now().isoformat(),
        'system_stats': dat_manager.get_system_stats(),
        'containers': dat_manager.get_container_status(),
        'desktop_health': dat_manager.get_desktop_health()
    })

@app.route('/api/restart-desktop', methods=['POST'])
def restart_desktop():
    """Restart the desktop container"""
    try:
        container = docker_client.containers.get('dat-alpine-desktop')
        container.restart()
        return jsonify({'success': True, 'message': 'Desktop container restarted'})
    except Exception as e:
        logger.error(f"Error restarting desktop: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/logs/<container_name>')
def get_logs(container_name):
    """Get logs for a specific container"""
    try:
        container = docker_client.containers.get(container_name)
        logs = container.logs(tail=100).decode('utf-8')
        return jsonify({'logs': logs})
    except Exception as e:
        logger.error(f"Error getting logs for {container_name}: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)
