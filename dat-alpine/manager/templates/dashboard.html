<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Manager - Desktop as a Service</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-healthy { background-color: #4CAF50; }
        .status-unhealthy { background-color: #FF9800; }
        .status-down { background-color: #F44336; }
        .status-error { background-color: #9E9E9E; }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.3s ease;
        }
        
        .progress-fill.warning {
            background: linear-gradient(90deg, #FF9800, #FFC107);
        }
        
        .progress-fill.danger {
            background: linear-gradient(90deg, #F44336, #FF5722);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: opacity 0.3s ease;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .connection-info {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-top: 15px;
            border-radius: 0 5px 5px 0;
        }
        
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        
        .error {
            color: #F44336;
            background: #ffebee;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .container-item {
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        
        .container-name {
            font-weight: bold;
            color: #333;
        }
        
        .container-details {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖥️ DAT Manager</h1>
            <p>Desktop as a Service - Alpine Linux Edition</p>
        </div>
        
        <div class="dashboard-grid">
            <!-- System Stats Card -->
            <div class="card">
                <h3>📊 System Resources</h3>
                <div id="system-stats" class="loading">Loading system stats...</div>
            </div>
            
            <!-- Desktop Health Card -->
            <div class="card">
                <h3>🖥️ Desktop Service</h3>
                <div id="desktop-health" class="loading">Checking desktop health...</div>
                <button class="btn" onclick="restartDesktop()" id="restart-btn" style="margin-top: 15px;">
                    🔄 Restart Desktop
                </button>
            </div>
            
            <!-- Connection Info Card -->
            <div class="card">
                <h3>🔗 Connection Information</h3>
                <div class="connection-info">
                    <strong>RDP Connection:</strong><br>
                    <code id="rdp-address">Loading...</code><br><br>
                    <strong>Default Credentials:</strong><br>
                    Username: <code>datuser</code><br>
                    Password: <code>datuser123</code>
                </div>
            </div>
        </div>
        
        <!-- Containers Card -->
        <div class="card">
            <h3>🐳 Container Status</h3>
            <div id="containers" class="loading">Loading container information...</div>
        </div>
    </div>

    <script>
        let statusData = {};
        
        async function fetchStatus() {
            try {
                const response = await fetch('/api/status');
                statusData = await response.json();
                updateDashboard();
            } catch (error) {
                console.error('Error fetching status:', error);
                showError('Failed to fetch system status');
            }
        }
        
        function updateDashboard() {
            updateSystemStats();
            updateDesktopHealth();
            updateContainers();
            updateConnectionInfo();
        }
        
        function updateSystemStats() {
            const container = document.getElementById('system-stats');
            const stats = statusData.system_stats;
            
            if (!stats || Object.keys(stats).length === 0) {
                container.innerHTML = '<div class="error">Unable to load system stats</div>';
                return;
            }
            
            container.innerHTML = `
                <div class="stat-item">
                    <span>CPU Usage</span>
                    <span>${stats.cpu_percent}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill ${getProgressClass(stats.cpu_percent)}" style="width: ${stats.cpu_percent}%"></div>
                </div>
                
                <div class="stat-item">
                    <span>Memory Usage</span>
                    <span>${stats.memory_used_gb}GB / ${stats.memory_total_gb}GB (${stats.memory_percent}%)</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill ${getProgressClass(stats.memory_percent)}" style="width: ${stats.memory_percent}%"></div>
                </div>
                
                <div class="stat-item">
                    <span>Disk Usage</span>
                    <span>${stats.disk_used_gb}GB / ${stats.disk_total_gb}GB (${stats.disk_percent}%)</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill ${getProgressClass(stats.disk_percent)}" style="width: ${stats.disk_percent}%"></div>
                </div>
            `;
        }
        
        function updateDesktopHealth() {
            const container = document.getElementById('desktop-health');
            const health = statusData.desktop_health;
            
            if (!health) {
                container.innerHTML = '<div class="error">Unable to check desktop health</div>';
                return;
            }
            
            const statusClass = `status-${health.status.replace('_', '-')}`;
            container.innerHTML = `
                <div class="stat-item">
                    <span><span class="status-indicator ${statusClass}"></span>Status</span>
                    <span>${health.status.toUpperCase()}</span>
                </div>
                <div style="margin-top: 10px; color: #666;">
                    ${health.message}
                </div>
            `;
        }
        
        function updateContainers() {
            const container = document.getElementById('containers');
            const containers = statusData.containers;
            
            if (!containers || containers.length === 0) {
                container.innerHTML = '<div class="error">No DAT containers found</div>';
                return;
            }
            
            container.innerHTML = containers.map(c => `
                <div class="container-item">
                    <div class="container-name">
                        <span class="status-indicator status-${c.status === 'running' ? 'healthy' : 'down'}"></span>
                        ${c.name}
                    </div>
                    <div class="container-details">
                        Status: ${c.status} | Image: ${c.image}
                    </div>
                </div>
            `).join('');
        }
        
        function updateConnectionInfo() {
            const addressElement = document.getElementById('rdp-address');
            addressElement.textContent = `${window.location.hostname}:3389`;
        }
        
        function getProgressClass(percentage) {
            if (percentage > 80) return 'danger';
            if (percentage > 60) return 'warning';
            return '';
        }
        
        async function restartDesktop() {
            const btn = document.getElementById('restart-btn');
            btn.disabled = true;
            btn.textContent = '🔄 Restarting...';
            
            try {
                const response = await fetch('/api/restart-desktop', { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    showSuccess('Desktop restarted successfully');
                    setTimeout(fetchStatus, 2000); // Refresh status after 2 seconds
                } else {
                    showError(result.message);
                }
            } catch (error) {
                showError('Failed to restart desktop');
            } finally {
                btn.disabled = false;
                btn.textContent = '🔄 Restart Desktop';
            }
        }
        
        function showError(message) {
            // Simple error display - could be enhanced with a proper notification system
            console.error(message);
        }
        
        function showSuccess(message) {
            // Simple success display - could be enhanced with a proper notification system
            console.log(message);
        }
        
        // Initialize dashboard
        fetchStatus();
        
        // Auto-refresh every 30 seconds
        setInterval(fetchStatus, 30000);
    </script>
</body>
</html>
