#!/bin/bash

# Create log directories
mkdir -p /var/log/supervisor
mkdir -p /var/log/xrdp
mkdir -p /var/run/xrdp
mkdir -p /var/run/dbus
mkdir -p /var/lib/dbus

# Generate XRDP keys if they don't exist
if [ ! -f /etc/xrdp/rsakeys.ini ]; then
    xrdp-keygen xrdp /etc/xrdp/rsakeys.ini
fi

# Create xrdp user and group if they don't exist
addgroup -S xrdp 2>/dev/null || true
adduser -S -G xrdp xrdp 2>/dev/null || true

# Set proper permissions
chown -R xrdp:xrdp /var/log/xrdp 2>/dev/null || true
chown -R xrdp:xrdp /var/run/xrdp 2>/dev/null || true
chmod 755 /var/run/xrdp

# Setup D-Bus
if [ ! -f /var/lib/dbus/machine-id ]; then
    dbus-uuidgen > /var/lib/dbus/machine-id
fi

# Create X11 directory
mkdir -p /tmp/.X11-unix
chmod 1777 /tmp/.X11-unix

# Start supervisor
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
