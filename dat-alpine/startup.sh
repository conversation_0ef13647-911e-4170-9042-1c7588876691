#!/bin/bash

# Create log directories
mkdir -p /var/log/supervisor
mkdir -p /var/log/xrdp
mkdir -p /var/run/xrdp

# Generate XRDP keys if they don't exist
if [ ! -f /etc/xrdp/rsakeys.ini ]; then
    xrdp-keygen xrdp /etc/xrdp/rsakeys.ini
fi

# Set proper permissions
chown -R xrdp:xrdp /var/log/xrdp
chown -R xrdp:xrdp /var/run/xrdp
chmod 755 /var/run/xrdp

# Start D-Bus
mkdir -p /var/run/dbus
dbus-uuidgen > /var/lib/dbus/machine-id
dbus-daemon --system --fork

# Start supervisor
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
