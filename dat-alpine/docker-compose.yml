version: '3.8'

services:
  dat-desktop:
    build: .
    container_name: dat-alpine-desktop
    ports:
      - "3389:3389"
    environment:
      - DISPLAY=:1
    volumes:
      - dat_home:/home/<USER>
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    restart: unless-stopped
    networks:
      - dat-network
    healthcheck:
      test: ["CMD", "netstat", "-tlnp", "|", "grep", ":3389"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  dat-manager:
    build: 
      context: ./manager
    container_name: dat-manager
    ports:
      - "8080:8080"
    environment:
      - DAT_DESKTOP_HOST=dat-desktop
      - DAT_DESKTOP_PORT=3389
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    restart: unless-stopped
    networks:
      - dat-network
    depends_on:
      - dat-desktop

volumes:
  dat_home:

networks:
  dat-network:
    driver: bridge
