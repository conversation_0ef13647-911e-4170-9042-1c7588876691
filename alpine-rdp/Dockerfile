# 🏔️ Ultra-Lightweight Alpine RDP Container for DAT Load Board
FROM alpine:3.20

# Update repositories and install minimal packages for XFCE + RDP + Chrome
RUN apk update && apk add --no-cache \
    xfce4 \
    xfce4-terminal \
    xrdp \
    py3-supervisor \
    dbus \
    font-noto \
    chromium \
    sudo \
    shadow \
    bash \
    curl \
    openssh \
    && rm -rf /var/cache/apk/*

# Create manager user
RUN adduser -D -s /bin/bash manager && \
    echo "manager:qTi8CBkYgGiCNupJ" | chpasswd && \
    adduser manager wheel && \
    echo "%wheel ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers

# Configure XRDP
RUN sed -i 's/3389/3390/g' /etc/xrdp/xrdp.ini && \
    echo "exec startxfce4" > /etc/xrdp/startwm.sh && \
    chmod +x /etc/xrdp/startwm.sh

# Create XFCE autostart for Chrome
RUN mkdir -p /home/<USER>/.config/autostart && \
    echo '[Desktop Entry]\n\
Type=Application\n\
Name=Chrome DAT Load Board\n\
Exec=chromium --no-sandbox --start-maximized https://power.dat.com/search/loads\n\
Hidden=false\n\
NoDisplay=false\n\
X-GNOME-Autostart-enabled=true' > /home/<USER>/.config/autostart/chrome-dat.desktop && \
    chown -R manager:manager /home/<USER>/.config

# Configure supervisor
RUN mkdir -p /etc/supervisor/conf.d && \
    echo '[supervisord]\n\
nodaemon=true\n\
user=root\n\
\n\
[program:dbus]\n\
command=/usr/bin/dbus-daemon --system --nofork\n\
user=root\n\
autostart=true\n\
autorestart=true\n\
\n\
[program:xrdp-sesman]\n\
command=/usr/sbin/xrdp-sesman --nodaemon\n\
user=root\n\
autostart=true\n\
autorestart=true\n\
\n\
[program:xrdp]\n\
command=/usr/sbin/xrdp --nodaemon\n\
user=root\n\
autostart=true\n\
autorestart=true' > /etc/supervisor/conf.d/services.conf

# Create startup script
RUN echo '#!/bin/bash\n\
# Generate host keys if they do not exist\n\
if [ ! -f /etc/ssh/ssh_host_rsa_key ]; then\n\
    ssh-keygen -A\n\
fi\n\
\n\
# Start supervisor\n\
exec /usr/bin/supervisord -c /etc/supervisord.conf' > /start.sh && \
    chmod +x /start.sh

EXPOSE 3390

CMD ["/start.sh"]
