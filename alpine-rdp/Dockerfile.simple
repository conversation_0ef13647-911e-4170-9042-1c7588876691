# 🏔️ Ultra-Lightweight Alpine RDP Container for DAT Load Board
FROM alpine:3.19

# Install essential packages
RUN apk update && apk add --no-cache \
    bash \
    curl \
    sudo \
    shadow \
    dbus \
    supervisor \
    openssh \
    && rm -rf /var/cache/apk/*

# Install XFCE desktop
RUN apk add --no-cache \
    xfce4 \
    xfce4-terminal \
    xfce4-session \
    xfce4-settings \
    xfce4-panel \
    xfce4-whiskermenu-plugin \
    thunar \
    && rm -rf /var/cache/apk/*

# Install RDP server
RUN apk add --no-cache \
    xrdp \
    xorgxrdp \
    && rm -rf /var/cache/apk/*

# Install Chromium
RUN apk add --no-cache \
    chromium \
    font-noto \
    && rm -rf /var/cache/apk/*

# Create manager user
RUN adduser -D -s /bin/bash manager && \
    echo "manager:qTi8CBkYgGiCNupJ" | chpasswd && \
    adduser manager wheel && \
    echo "%wheel ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers

# Configure XRDP
RUN sed -i 's/3389/3390/g' /etc/xrdp/xrdp.ini && \
    echo "exec startxfce4" > /etc/xrdp/startwm.sh && \
    chmod +x /etc/xrdp/startwm.sh

# Create XFCE autostart for Chrome
RUN mkdir -p /home/<USER>/.config/autostart && \
    echo '[Desktop Entry]\n\
Type=Application\n\
Name=Chrome DAT Load Board\n\
Exec=chromium --no-sandbox --start-maximized https://power.dat.com/search/loads\n\
Hidden=false\n\
NoDisplay=false\n\
X-GNOME-Autostart-enabled=true' > /home/<USER>/.config/autostart/chrome-dat.desktop && \
    chown -R manager:manager /home/<USER>/.config

# Create simple startup script
RUN echo '#!/bin/bash\n\
# Initialize D-Bus\n\
mkdir -p /var/run/dbus\n\
dbus-daemon --system --fork\n\
\n\
# Generate SSH keys if needed\n\
ssh-keygen -A 2>/dev/null || true\n\
\n\
# Start XRDP services\n\
/usr/sbin/xrdp-sesman\n\
/usr/sbin/xrdp --nodaemon' > /start.sh && \
    chmod +x /start.sh

EXPOSE 3390

CMD ["/start.sh"]
